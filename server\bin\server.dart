import 'dart:io';
import 'dart:convert';
import 'dart:async';

// Import core server packages
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:shelf_static/shelf_static.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:logging/logging.dart';

// Import shared package (will be available after pub get)
// import 'package:shared/shared.dart';

// Server configuration
class ServerConfig {
  static const String version = '2.0.0';
  static const String name = 'Quester Server';
  static final Logger logger = Logger('QuesterServer');

  // Environment variables
  static String get host => Platform.environment['HOST'] ?? '0.0.0.0';
  static int get port => int.parse(Platform.environment['PORT'] ?? '8080');
  static String get environment => Platform.environment['NODE_ENV'] ?? 'development';
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
}

// WebSocket connection manager
class WebSocketManager {
  static final Map<String, WebSocketChannel> _connections = {};
  static final Logger _logger = Logger('WebSocketManager');

  static void addConnection(String id, WebSocketChannel channel) {
    _connections[id] = channel;
    _logger.info('WebSocket connection added: $id');
  }

  static void removeConnection(String id) {
    _connections.remove(id);
    _logger.info('WebSocket connection removed: $id');
  }

  static void broadcast(Map<String, dynamic> message) {
    final messageJson = jsonEncode(message);
    for (final connection in _connections.values) {
      try {
        connection.sink.add(messageJson);
      } catch (e) {
        _logger.warning('Failed to send message to WebSocket connection: $e');
      }
    }
  }

  static int get connectionCount => _connections.length;
}

// Main server class
class QuesterServer {
  late final Router _router;
  late final Handler _handler;
  final Logger _logger = Logger('QuesterServer');

  QuesterServer() {
    _setupLogging();
    _setupRouter();
    _setupHandler();
  }

  void _setupLogging() {
    Logger.root.level = ServerConfig.isDevelopment ? Level.ALL : Level.INFO;
    Logger.root.onRecord.listen((record) {
      print('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  void _setupRouter() {
    _router = Router()
      // Health check endpoints
      ..get('/', _rootHandler)
      ..get('/health', _healthHandler)
      ..get('/version', _versionHandler)

      // API endpoints
      ..mount('/api/', _createApiRouter())

      // WebSocket endpoints
      ..get('/ws/notifications', webSocketHandler((webSocket) => _handleNotificationWebSocket(webSocket)))
      ..get('/ws/updates', webSocketHandler((webSocket) => _handleUpdatesWebSocket(webSocket)))

      // Static file serving
      ..mount('/static/', createStaticHandler('public', defaultDocument: 'index.html'));
  }

  void _setupHandler() {
    _handler = Pipeline()
        .addMiddleware(_corsMiddleware)
        .addMiddleware(logRequests(logger: _logger.info))
        .addMiddleware(_errorHandler)
        .addMiddleware(_authMiddleware)
        .addHandler(_router.call);
  }

  // CORS middleware
  Middleware get _corsMiddleware => (innerHandler) {
    return (request) async {
      final response = await innerHandler(request);

      return response.change(headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept, Authorization',
        'Access-Control-Max-Age': '86400',
        ...response.headers,
      });
    };
  };

  Router _createApiRouter() {
    return Router()
      // Authentication endpoints
      ..post('/auth/register', _registerHandler)
      ..post('/auth/login', _loginHandler)
      ..post('/auth/logout', _logoutHandler)
      ..post('/auth/refresh', _refreshTokenHandler)

      // User management endpoints
      ..get('/users/me', _getCurrentUserHandler)
      ..get('/users/<id>', _getUserByIdHandler)
      ..put('/users/<id>', _updateUserHandler)
      ..delete('/users/<id>', _deleteUserHandler)

      // Quest management endpoints
      ..get('/quests', _getQuestsHandler)
      ..post('/quests', _createQuestHandler)
      ..get('/quests/<id>', _getQuestByIdHandler)
      ..put('/quests/<id>', _updateQuestHandler)
      ..delete('/quests/<id>', _deleteQuestHandler)
      ..post('/quests/<id>/complete', _completeQuestHandler)

      // Notification endpoints
      ..get('/notifications', _getNotificationsHandler)
      ..post('/notifications', _createNotificationHandler)
      ..put('/notifications/<id>/read', _markNotificationReadHandler)
      ..delete('/notifications/<id>', _deleteNotificationHandler)

      // Dashboard endpoints
      ..get('/dashboard/stats', _getDashboardStatsHandler)
      ..get('/dashboard/activity', _getRecentActivityHandler)

      // Achievement endpoints
      ..get('/achievements', _getAchievementsHandler)
      ..get('/achievements/<id>', _getAchievementByIdHandler)
      ..post('/achievements', _createAchievementHandler)

      // Leaderboard endpoints
      ..get('/leaderboards', _getLeaderboardsHandler)
      ..get('/leaderboards/<id>', _getLeaderboardByIdHandler);
  }

  // Middleware
  Middleware get _errorHandler => (innerHandler) {
    return (request) async {
      try {
        return await innerHandler(request);
      } catch (error, stackTrace) {
        _logger.severe('Unhandled error: $error', error, stackTrace);

        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'message': ServerConfig.isDevelopment
                ? error.toString()
                : 'Internal server error',
            'timestamp': DateTime.now().toIso8601String(),
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    };
  };

  Middleware get _authMiddleware => (innerHandler) {
    return (request) async {
      // Skip auth for public endpoints
      final publicPaths = [
        '/',
        '/health',
        '/version',
        '/api/auth/register',
        '/api/auth/login',
        '/api/auth/refresh',
      ];

      if (publicPaths.contains(request.url.path) ||
          request.url.path.startsWith('/static/')) {
        return await innerHandler(request);
      }

      // Check for authorization header
      final authHeader = request.headers['authorization'];
      if (authHeader == null || !authHeader.startsWith('Bearer ')) {
        return Response.unauthorized(
          jsonEncode({
            'success': false,
            'message': 'Authorization required',
            'timestamp': DateTime.now().toIso8601String(),
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }

      // TODO: Implement JWT token validation
      // For now, just pass through
      return await innerHandler(request);
    };
  };

  // Basic handlers
  Response _rootHandler(Request request) {
    final data = {
      'name': ServerConfig.name,
      'version': ServerConfig.version,
      'environment': ServerConfig.environment,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Quester Server is running',
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Response _healthHandler(Request request) {
    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Server is healthy',
        'data': {
          'status': 'healthy',
          'uptime': DateTime.now().toIso8601String(),
          'connections': WebSocketManager.connectionCount,
        },
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Response _versionHandler(Request request) {
    return Response.ok(
      jsonEncode({
        'success': true,
        'data': {
          'version': ServerConfig.version,
          'name': ServerConfig.name,
        },
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Placeholder handlers (to be implemented)
  Future<Response> _registerHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Registration endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _loginHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Login endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _logoutHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': true,
        'message': 'Logged out successfully',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _refreshTokenHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Token refresh endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // User handlers
  Future<Response> _getCurrentUserHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get current user endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getUserByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get user by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _updateUserHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Update user endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteUserHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete user endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Quest handlers
  Future<Response> _getQuestsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get quests endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _createQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Create quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getQuestByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get quest by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _updateQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Update quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _completeQuestHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Complete quest endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Notification handlers
  Future<Response> _getNotificationsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get notifications endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _createNotificationHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Create notification endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _markNotificationReadHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Mark notification read endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _deleteNotificationHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Delete notification endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Dashboard handlers
  Future<Response> _getDashboardStatsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get dashboard stats endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getRecentActivityHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get recent activity endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Achievement handlers
  Future<Response> _getAchievementsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get achievements endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getAchievementByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get achievement by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _createAchievementHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Create achievement endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // Leaderboard handlers
  Future<Response> _getLeaderboardsHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get leaderboards endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  Future<Response> _getLeaderboardByIdHandler(Request request) async {
    return Response.ok(
      jsonEncode({
        'success': false,
        'message': 'Get leaderboard by ID endpoint not yet implemented',
        'timestamp': DateTime.now().toIso8601String(),
      }),
      headers: {'Content-Type': 'application/json'},
    );
  }

  // WebSocket handlers
  void _handleNotificationWebSocket(WebSocketChannel webSocket) {
    final connectionId = DateTime.now().millisecondsSinceEpoch.toString();
    WebSocketManager.addConnection(connectionId, webSocket);

    webSocket.stream.listen(
      (message) {
        _logger.info('Received WebSocket message: $message');
        // Handle incoming WebSocket messages
      },
      onDone: () {
        WebSocketManager.removeConnection(connectionId);
        _logger.info('WebSocket connection closed: $connectionId');
      },
      onError: (error) {
        _logger.warning('WebSocket error: $error');
        WebSocketManager.removeConnection(connectionId);
      },
    );
  }

  void _handleUpdatesWebSocket(WebSocketChannel webSocket) {
    final connectionId = DateTime.now().millisecondsSinceEpoch.toString();
    WebSocketManager.addConnection(connectionId, webSocket);

    webSocket.stream.listen(
      (message) {
        _logger.info('Received WebSocket message: $message');
        // Handle incoming WebSocket messages
      },
      onDone: () {
        WebSocketManager.removeConnection(connectionId);
        _logger.info('WebSocket connection closed: $connectionId');
      },
      onError: (error) {
        _logger.warning('WebSocket error: $error');
        WebSocketManager.removeConnection(connectionId);
      },
    );
  }

  // Start the server
  Future<void> start() async {
    try {
      final server = await serve(_handler, ServerConfig.host, ServerConfig.port);
      _logger.info('${ServerConfig.name} v${ServerConfig.version} listening on ${server.address.host}:${server.port}');
      _logger.info('Environment: ${ServerConfig.environment}');
    } catch (error, stackTrace) {
      _logger.severe('Failed to start server: $error', error, stackTrace);
      rethrow;
    }
  }
}

// Main entry point
void main(List<String> args) async {
  final server = QuesterServer();
  await server.start();
}
