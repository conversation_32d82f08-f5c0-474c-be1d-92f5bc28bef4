import 'package:shared/shared.dart' hide ApiService;

import '../services/api_service.dart';

/// Repository for quest-related operations
class QuestRepository {
  final ApiService _apiService;

  QuestRepository({
    required ApiService apiService,
  }) : _apiService = apiService;

  /// Get all quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getQuests({
    String? category,
    String? difficulty,
    String? status,
    String? sortBy,
    String? sortOrder,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (category != null) queryParams['category'] = category;
    if (difficulty != null) queryParams['difficulty'] = difficulty;
    if (status != null) queryParams['status'] = status;
    if (sortBy != null) queryParams['sortBy'] = sortBy;
    if (sortOrder != null) queryParams['sortOrder'] = sortOrder;

    return await _apiService.get('/quests', queryParameters: queryParams);
  }

  /// Get quest by ID
  Future<ApiResponse<Map<String, dynamic>>> getQuestById(String questId) async {
    return await _apiService.get('/quests/$questId');
  }

  /// Create a new quest
  Future<ApiResponse<Map<String, dynamic>>> createQuest({
    required String title,
    required String description,
    required String category,
    required String difficulty,
    int? xpReward,
    int? coinReward,
    DateTime? deadline,
    List<Map<String, dynamic>>? requirements,
    Map<String, dynamic>? metadata,
  }) async {
    final data = <String, dynamic>{
      'title': title,
      'description': description,
      'category': category,
      'difficulty': difficulty,
    };

    if (xpReward != null) data['xpReward'] = xpReward;
    if (coinReward != null) data['coinReward'] = coinReward;
    if (deadline != null) data['deadline'] = deadline.toIso8601String();
    if (requirements != null) data['requirements'] = requirements;
    if (metadata != null) data['metadata'] = metadata;

    return await _apiService.post('/quests', data: data);
  }

  /// Update quest
  Future<ApiResponse<Map<String, dynamic>>> updateQuest({
    required String questId,
    String? title,
    String? description,
    String? category,
    String? difficulty,
    int? xpReward,
    int? coinReward,
    DateTime? deadline,
    List<Map<String, dynamic>>? requirements,
    Map<String, dynamic>? metadata,
  }) async {
    final data = <String, dynamic>{};

    if (title != null) data['title'] = title;
    if (description != null) data['description'] = description;
    if (category != null) data['category'] = category;
    if (difficulty != null) data['difficulty'] = difficulty;
    if (xpReward != null) data['xpReward'] = xpReward;
    if (coinReward != null) data['coinReward'] = coinReward;
    if (deadline != null) data['deadline'] = deadline.toIso8601String();
    if (requirements != null) data['requirements'] = requirements;
    if (metadata != null) data['metadata'] = metadata;

    return await _apiService.put('/quests/$questId', data: data);
  }

  /// Delete quest
  Future<ApiResponse<void>> deleteQuest(String questId) async {
    return await _apiService.delete('/quests/$questId');
  }

  /// Start quest
  Future<ApiResponse<Map<String, dynamic>>> startQuest(String questId) async {
    return await _apiService.post('/quests/$questId/start');
  }

  /// Complete quest
  Future<ApiResponse<Map<String, dynamic>>> completeQuest({
    required String questId,
    Map<String, dynamic>? completionData,
  }) async {
    final data = <String, dynamic>{};
    if (completionData != null) data['completionData'] = completionData;

    return await _apiService.post('/quests/$questId/complete', data: data);
  }

  /// Abandon quest
  Future<ApiResponse<void>> abandonQuest(String questId) async {
    return await _apiService.post('/quests/$questId/abandon');
  }

  /// Get quest progress
  Future<ApiResponse<Map<String, dynamic>>> getQuestProgress(String questId) async {
    return await _apiService.get('/quests/$questId/progress');
  }

  /// Update quest progress
  Future<ApiResponse<Map<String, dynamic>>> updateQuestProgress({
    required String questId,
    required Map<String, dynamic> progressData,
  }) async {
    return await _apiService.put('/quests/$questId/progress', data: progressData);
  }

  /// Get quest categories
  Future<ApiResponse<List<Map<String, dynamic>>>> getQuestCategories() async {
    return await _apiService.get('/quests/categories');
  }

  /// Get quest difficulties
  Future<ApiResponse<List<Map<String, dynamic>>>> getQuestDifficulties() async {
    return await _apiService.get('/quests/difficulties');
  }

  /// Search quests
  Future<ApiResponse<List<Map<String, dynamic>>>> searchQuests({
    required String query,
    String? category,
    String? difficulty,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, String>{
      'q': query,
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (category != null) queryParams['category'] = category;
    if (difficulty != null) queryParams['difficulty'] = difficulty;

    return await _apiService.get('/quests/search', queryParameters: queryParams);
  }

  /// Get featured quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getFeaturedQuests({
    int limit = 10,
  }) async {
    return await _apiService.get('/quests/featured', queryParameters: {
      'limit': limit.toString(),
    });
  }

  /// Get popular quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getPopularQuests({
    int limit = 10,
  }) async {
    return await _apiService.get('/quests/popular', queryParameters: {
      'limit': limit.toString(),
    });
  }

  /// Get recent quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getRecentQuests({
    int limit = 10,
  }) async {
    return await _apiService.get('/quests/recent', queryParameters: {
      'limit': limit.toString(),
    });
  }

  /// Get recommended quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getRecommendedQuests({
    int limit = 10,
  }) async {
    return await _apiService.get('/quests/recommended', queryParameters: {
      'limit': limit.toString(),
    });
  }

  /// Like quest
  Future<ApiResponse<void>> likeQuest(String questId) async {
    return await _apiService.post('/quests/$questId/like');
  }

  /// Unlike quest
  Future<ApiResponse<void>> unlikeQuest(String questId) async {
    return await _apiService.delete('/quests/$questId/like');
  }

  /// Get quest likes
  Future<ApiResponse<Map<String, dynamic>>> getQuestLikes(String questId) async {
    return await _apiService.get('/quests/$questId/likes');
  }

  /// Add quest to favorites
  Future<ApiResponse<void>> addToFavorites(String questId) async {
    return await _apiService.post('/quests/$questId/favorite');
  }

  /// Remove quest from favorites
  Future<ApiResponse<void>> removeFromFavorites(String questId) async {
    return await _apiService.delete('/quests/$questId/favorite');
  }

  /// Get favorite quests
  Future<ApiResponse<List<Map<String, dynamic>>>> getFavoriteQuests({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/quests/favorites', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Share quest
  Future<ApiResponse<Map<String, dynamic>>> shareQuest({
    required String questId,
    required String platform,
    String? message,
  }) async {
    return await _apiService.post('/quests/$questId/share', data: {
      'platform': platform,
      'message': message,
    });
  }

  /// Report quest
  Future<ApiResponse<void>> reportQuest({
    required String questId,
    required String reason,
    String? description,
  }) async {
    return await _apiService.post('/quests/$questId/report', data: {
      'reason': reason,
      'description': description,
    });
  }

  /// Get quest comments
  Future<ApiResponse<List<Map<String, dynamic>>>> getQuestComments({
    required String questId,
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/quests/$questId/comments', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }

  /// Add quest comment
  Future<ApiResponse<Map<String, dynamic>>> addQuestComment({
    required String questId,
    required String content,
    String? parentCommentId,
  }) async {
    final data = <String, dynamic>{
      'content': content,
    };
    if (parentCommentId != null) data['parentCommentId'] = parentCommentId;

    return await _apiService.post('/quests/$questId/comments', data: data);
  }

  /// Update quest comment
  Future<ApiResponse<Map<String, dynamic>>> updateQuestComment({
    required String questId,
    required String commentId,
    required String content,
  }) async {
    return await _apiService.put('/quests/$questId/comments/$commentId', data: {
      'content': content,
    });
  }

  /// Delete quest comment
  Future<ApiResponse<void>> deleteQuestComment({
    required String questId,
    required String commentId,
  }) async {
    return await _apiService.delete('/quests/$questId/comments/$commentId');
  }

  /// Get quest statistics
  Future<ApiResponse<Map<String, dynamic>>> getQuestStatistics(String questId) async {
    return await _apiService.get('/quests/$questId/statistics');
  }

  /// Get quest leaderboard
  Future<ApiResponse<List<Map<String, dynamic>>>> getQuestLeaderboard({
    required String questId,
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get('/quests/$questId/leaderboard', queryParameters: {
      'page': page.toString(),
      'limit': limit.toString(),
    });
  }
}
