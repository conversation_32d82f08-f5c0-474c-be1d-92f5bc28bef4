import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

/// WebSocket service for real-time communication
class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _pingTimer;
  Timer? _reconnectTimer;
  
  bool _isConnected = false;
  bool _isConnecting = false;
  int _reconnectAttempts = 0;
  String? _authToken;
  
  // Configuration
  static const String _baseUrl = 'ws://localhost:8080';
  static const Duration _pingInterval = Duration(seconds: 30);
  static const Duration _reconnectDelay = Duration(seconds: 5);
  static const int _maxReconnectAttempts = 10;
  
  // Stream controllers for different message types
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _notificationController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();
  
  // Public streams
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;
  Stream<bool> get connectionStream => _connectionController.stream;
  
  // Getters
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  
  /// Connect to WebSocket server
  Future<void> connect(String authToken) async {
    if (_isConnected || _isConnecting) {
      return;
    }
    
    _authToken = authToken;
    _isConnecting = true;
    _connectionController.add(false);
    
    try {
      debugPrint('🔌 Connecting to WebSocket server...');
      
      _channel = IOWebSocketChannel.connect(
        Uri.parse(_baseUrl),
        protocols: ['websocket'],
      );
      
      // Listen to messages
      _subscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
      
      // Send authentication message
      await _authenticate();
      
      // Start ping timer
      _startPingTimer();
      
      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;
      _connectionController.add(true);
      
      debugPrint('✅ WebSocket connected successfully');
      
    } catch (error) {
      debugPrint('❌ WebSocket connection failed: $error');
      _isConnecting = false;
      _connectionController.add(false);
      _scheduleReconnect();
    }
  }
  
  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    debugPrint('🔌 Disconnecting from WebSocket server...');
    
    _isConnected = false;
    _isConnecting = false;
    _reconnectAttempts = _maxReconnectAttempts; // Prevent reconnection
    
    _pingTimer?.cancel();
    _reconnectTimer?.cancel();
    
    await _subscription?.cancel();
    await _channel?.sink.close();
    
    _channel = null;
    _subscription = null;
    _pingTimer = null;
    _reconnectTimer = null;
    
    _connectionController.add(false);
    
    debugPrint('✅ WebSocket disconnected');
  }
  
  /// Send message to server
  void sendMessage(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      print('⚠️ Cannot send message: WebSocket not connected');
      return;
    }
    
    try {
      final jsonMessage = jsonEncode(message);
      _channel!.sink.add(jsonMessage);
      print('📤 Sent message: ${message['type']}');
    } catch (error) {
      print('❌ Failed to send message: $error');
    }
  }
  
  /// Subscribe to a channel
  void subscribe(String channel) {
    sendMessage({
      'type': 'subscribe',
      'channel': channel,
    });
  }
  
  /// Unsubscribe from a channel
  void unsubscribe(String channel) {
    sendMessage({
      'type': 'unsubscribe',
      'channel': channel,
    });
  }
  
  /// Handle incoming messages
  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message as String) as Map<String, dynamic>;
      final messageType = data['type'] as String?;
      
      print('📥 Received message: $messageType');
      
      switch (messageType) {
        case 'welcome':
          print('👋 ${data['message']}');
          break;
          
        case 'auth_success':
          print('🔐 Authentication successful');
          break;
          
        case 'auth_error':
          print('❌ Authentication failed: ${data['message']}');
          break;
          
        case 'notification':
          _notificationController.add(data['data'] as Map<String, dynamic>);
          break;
          
        case 'pong':
          // Handle ping response
          break;
          
        case 'error':
          print('❌ Server error: ${data['message']}');
          break;
          
        default:
          _messageController.add(data);
      }
    } catch (error) {
      print('❌ Failed to parse message: $error');
    }
  }
  
  /// Handle WebSocket errors
  void _handleError(dynamic error) {
    print('❌ WebSocket error: $error');
    _isConnected = false;
    _connectionController.add(false);
    _scheduleReconnect();
  }
  
  /// Handle WebSocket disconnection
  void _handleDisconnection() {
    print('🔌 WebSocket disconnected');
    _isConnected = false;
    _connectionController.add(false);
    _scheduleReconnect();
  }
  
  /// Authenticate with the server
  Future<void> _authenticate() async {
    if (_authToken == null) {
      throw Exception('No auth token available');
    }
    
    sendMessage({
      'type': 'auth',
      'token': _authToken,
    });
  }
  
  /// Start ping timer to keep connection alive
  void _startPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(_pingInterval, (timer) {
      if (_isConnected) {
        sendMessage({'type': 'ping'});
      }
    });
  }
  
  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      print('❌ Max reconnection attempts reached');
      return;
    }
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      _reconnectAttempts++;
      print('🔄 Reconnection attempt $_reconnectAttempts/$_maxReconnectAttempts');
      
      if (_authToken != null) {
        connect(_authToken!);
      }
    });
  }
  
  /// Dispose resources
  void dispose() {
    _messageController.close();
    _notificationController.close();
    _connectionController.close();
    disconnect();
  }
}
