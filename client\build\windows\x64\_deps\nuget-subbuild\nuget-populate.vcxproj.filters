﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e35e2d3d013c0ee5b7e2cb2a82948646\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\80a280cff038fcad9e2bdaba466edde7\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\e62d647c407f689b7bf11e09aa1cb930\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\quester\client\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{EE31E837-F030-3739-B924-041D80F24FC7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
