/// API-related constants and configuration
class ApiConstants {
  // Base URLs
  static const String baseUrl = 'http://localhost:3000/api';
  static const String wsBaseUrl = 'ws://localhost:8080/ws';
  
  // API Versions
  static const String apiVersion = 'v1';
  static const String apiVersionHeader = 'X-API-Version';
  
  // Authentication endpoints
  static const String authRegister = '/auth/register';
  static const String authLogin = '/auth/login';
  static const String authLogout = '/auth/logout';
  static const String authRefresh = '/auth/refresh';
  static const String authMe = '/auth/me';
  
  // User management endpoints
  static const String users = '/users';
  static const String userById = '/users/{id}';
  static const String userProfile = '/users/{id}/profile';
  static const String userAvatar = '/users/{id}/avatar';
  static const String userQuests = '/users/{id}/quests';
  static const String userStats = '/users/{id}/stats';
  
  // Quest management endpoints
  static const String quests = '/quests';
  static const String questById = '/quests/{id}';
  static const String questComplete = '/quests/{id}/complete';
  static const String questAssign = '/quests/{id}/assign';
  static const String questUnassign = '/quests/{id}/unassign';
  static const String questStats = '/quests/{id}/stats';
  
  // Notification endpoints
  static const String notifications = '/notifications';
  static const String notificationById = '/notifications/{id}';
  static const String notificationMarkRead = '/notifications/{id}/read';
  static const String notificationMarkAllRead = '/notifications/read-all';
  
  // Dashboard endpoints
  static const String dashboard = '/dashboard';
  static const String dashboardStats = '/dashboard/stats';
  static const String dashboardActivity = '/dashboard/activity';
  static const String dashboardHealth = '/dashboard/health';
  
  // Search endpoints
  static const String searchUsers = '/search/users';
  static const String searchQuests = '/search/quests';
  static const String searchAll = '/search';
  
  // File upload endpoints
  static const String uploadAvatar = '/upload/avatar';
  static const String uploadQuestImage = '/upload/quest-image';
  static const String uploadFile = '/upload/file';
  
  // WebSocket endpoints
  static const String wsNotifications = '/ws/notifications';
  static const String wsUpdates = '/ws/updates';
  static const String wsDashboard = '/ws/dashboard';
  
  // HTTP Headers
  static const String contentTypeJson = 'application/json';
  static const String contentTypeFormData = 'multipart/form-data';
  static const String authorizationHeader = 'Authorization';
  static const String bearerPrefix = 'Bearer ';
  static const String userAgentHeader = 'User-Agent';
  static const String acceptHeader = 'Accept';
  static const String acceptLanguageHeader = 'Accept-Language';
  
  // Request/Response headers
  static const String requestIdHeader = 'X-Request-ID';
  static const String correlationIdHeader = 'X-Correlation-ID';
  static const String timestampHeader = 'X-Timestamp';
  static const String checksumHeader = 'X-Checksum';
  
  // CORS headers
  static const String corsOriginHeader = 'Access-Control-Allow-Origin';
  static const String corsMethodsHeader = 'Access-Control-Allow-Methods';
  static const String corsHeadersHeader = 'Access-Control-Allow-Headers';
  static const String corsCredentialsHeader = 'Access-Control-Allow-Credentials';
  
  // Cache headers
  static const String cacheControlHeader = 'Cache-Control';
  static const String etagHeader = 'ETag';
  static const String lastModifiedHeader = 'Last-Modified';
  static const String ifNoneMatchHeader = 'If-None-Match';
  
  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Retry configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);
  static const Duration maxRetryDelay = Duration(seconds: 10);
  
  // Rate limiting
  static const int rateLimitRequests = 100;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const String pageParam = 'page';
  static const String pageSizeParam = 'pageSize';
  static const String sortByParam = 'sortBy';
  static const String sortOrderParam = 'sortOrder';
  
  // Query parameters
  static const String searchParam = 'q';
  static const String filterParam = 'filter';
  static const String includeParam = 'include';
  static const String excludeParam = 'exclude';
  
  // HTTP Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusAccepted = 202;
  static const int statusNoContent = 204;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusConflict = 409;
  static const int statusUnprocessableEntity = 422;
  static const int statusTooManyRequests = 429;
  static const int statusInternalServerError = 500;
  static const int statusBadGateway = 502;
  static const int statusServiceUnavailable = 503;
  
  // Error codes
  static const String errorInvalidCredentials = 'INVALID_CREDENTIALS';
  static const String errorTokenExpired = 'TOKEN_EXPIRED';
  static const String errorTokenInvalid = 'TOKEN_INVALID';
  static const String errorUserNotFound = 'USER_NOT_FOUND';
  static const String errorQuestNotFound = 'QUEST_NOT_FOUND';
  static const String errorValidationFailed = 'VALIDATION_FAILED';
  static const String errorPermissionDenied = 'PERMISSION_DENIED';
  static const String errorRateLimitExceeded = 'RATE_LIMIT_EXCEEDED';
  static const String errorServerError = 'SERVER_ERROR';
  
  // Content types
  static const String mimeTypeJson = 'application/json';
  static const String mimeTypeXml = 'application/xml';
  static const String mimeTypeFormUrlEncoded = 'application/x-www-form-urlencoded';
  static const String mimeTypeMultipartFormData = 'multipart/form-data';
  static const String mimeTypeTextPlain = 'text/plain';
  static const String mimeTypeTextHtml = 'text/html';
  
  // Image types
  static const String mimeTypeImageJpeg = 'image/jpeg';
  static const String mimeTypeImagePng = 'image/png';
  static const String mimeTypeImageGif = 'image/gif';
  static const String mimeTypeImageWebp = 'image/webp';
  
  // File size limits
  static const int maxAvatarSize = 5 * 1024 * 1024; // 5MB
  static const int maxQuestImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxFileSize = 50 * 1024 * 1024; // 50MB
  
  // Supported image formats
  static const List<String> supportedImageFormats = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  
  // API response formats
  static const String formatJson = 'json';
  static const String formatXml = 'xml';
  
  // Environment-specific URLs
  static String getBaseUrl(String environment) {
    switch (environment.toLowerCase()) {
      case 'development':
      case 'dev':
        return 'http://localhost:3000/api';
      case 'staging':
      case 'stage':
        return 'https://api-staging.quester.app';
      case 'production':
      case 'prod':
        return 'https://api.quester.app';
      default:
        return baseUrl;
    }
  }
  
  static String getWebSocketUrl(String environment) {
    switch (environment.toLowerCase()) {
      case 'development':
      case 'dev':
        return 'ws://localhost:8080/ws';
      case 'staging':
      case 'stage':
        return 'wss://ws-staging.quester.app';
      case 'production':
      case 'prod':
        return 'wss://ws.quester.app';
      default:
        return wsBaseUrl;
    }
  }
  
  // Build URL with parameters
  static String buildUrl(String endpoint, {Map<String, String>? pathParams, Map<String, String>? queryParams}) {
    String url = endpoint;
    
    // Replace path parameters
    if (pathParams != null) {
      pathParams.forEach((key, value) {
        url = url.replaceAll('{$key}', value);
      });
    }
    
    // Add query parameters
    if (queryParams != null && queryParams.isNotEmpty) {
      final query = queryParams.entries
          .map((entry) => '${Uri.encodeComponent(entry.key)}=${Uri.encodeComponent(entry.value)}')
          .join('&');
      url += '?$query';
    }
    
    return url;
  }
}
