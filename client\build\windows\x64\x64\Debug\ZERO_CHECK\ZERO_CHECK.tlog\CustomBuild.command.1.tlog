^D:\QUESTER\CLIENT\BUILD\WINDOWS\X64\CMAKEFILES\A24B4BF49F9C87A5365A203684370DBA\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/quester/client/windows -BD:/quester/client/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/quester/client/build/windows/x64/client.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
