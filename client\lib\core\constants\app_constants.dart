/// Application-wide constants for the Quester Flutter client
class AppConstants {
  // App Information
  static const String appName = 'Quester';
  static const String appVersion = '2.0.0';
  static const String appDescription = 'Modern quest management application';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:8080';
  static const String apiVersion = 'v1';
  static const String apiBaseUrl = '$baseUrl/api';
  
  // WebSocket Configuration
  static const String wsBaseUrl = 'ws://localhost:8080';
  static const String wsNotificationsUrl = '$wsBaseUrl/ws/notifications';
  static const String wsUpdatesUrl = '$wsBaseUrl/ws/updates';
  
  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String notificationSettingsKey = 'notification_settings';
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  static const double defaultMargin = 16.0;
  static const double smallMargin = 8.0;
  static const double largeMargin = 24.0;
  
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  
  static const double defaultElevation = 4.0;
  static const double smallElevation = 2.0;
  static const double largeElevation = 8.0;
  
  // App Bar Constants
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 4.0;
  static const String appBarTitle = 'Quester';
  static const String appBarLogoPath = 'assets/logos/quester_logo.png';
  
  // Navigation Constants
  static const int maxNavigationItems = 5;
  static const double navigationIconSize = 24.0;
  static const double navigationLabelFontSize = 12.0;
  static const double bottomNavigationHeight = 60.0;
  static const double sideNavigationWidth = 280.0;
  
  // Sidebar Constants
  static const double sidebarWidth = 320.0;
  static const double sidebarMinWidth = 280.0;
  static const double sidebarMaxWidth = 400.0;
  static const Duration sidebarAnimationDuration = Duration(milliseconds: 250);
  
  // Notification Constants
  static const int maxNotificationDisplayCount = 5;
  static const Duration notificationAutoHideDuration = Duration(seconds: 5);
  static const double notificationDropdownWidth = 360.0;
  static const double notificationDropdownMaxHeight = 400.0;
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Breakpoints
  static const double mobileBreakpoint = 450.0;
  static const double tabletBreakpoint = 800.0;
  static const double desktopBreakpoint = 1200.0;
  static const double largeDesktopBreakpoint = 1920.0;
  
  // Grid Constants
  static const int mobileGridColumns = 1;
  static const int tabletGridColumns = 2;
  static const int desktopGridColumns = 3;
  static const int largeDesktopGridColumns = 4;
  
  // Image Constants
  static const double defaultAvatarSize = 40.0;
  static const double largeAvatarSize = 80.0;
  static const double smallAvatarSize = 24.0;
  
  // Loading Constants
  static const Duration loadingTimeout = Duration(seconds: 30);
  static const Duration refreshTimeout = Duration(seconds: 10);
  
  // Pagination Constants
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation Constants
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 500;
  
  // File Upload Constants
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  
  // Cache Constants
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100;
  
  // Error Messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your connection.';
  static const String timeoutErrorMessage = 'Request timed out. Please try again.';
  static const String unauthorizedErrorMessage = 'You are not authorized to perform this action.';
  static const String notFoundErrorMessage = 'The requested resource was not found.';
  
  // Success Messages
  static const String genericSuccessMessage = 'Operation completed successfully.';
  static const String saveSuccessMessage = 'Changes saved successfully.';
  static const String deleteSuccessMessage = 'Item deleted successfully.';
  static const String updateSuccessMessage = 'Item updated successfully.';
  static const String createSuccessMessage = 'Item created successfully.';
  
  // Feature Flags
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableWebSocket = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = false;
  
  // Development Constants
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = false;
  
  // Social Features
  static const int maxFriendsCount = 1000;
  static const int maxGroupSize = 50;
  static const Duration friendRequestExpiration = Duration(days: 30);
  
  // Quest Constants
  static const int maxActiveQuests = 10;
  static const int maxQuestTitleLength = 100;
  static const int maxQuestDescriptionLength = 1000;
  static const Duration questTimeout = Duration(days: 30);
  
  // Achievement Constants
  static const int maxAchievementsPerCategory = 50;
  static const int maxAchievementRequirements = 10;
  static const Duration achievementAnimationDuration = Duration(milliseconds: 800);
  
  // Leaderboard Constants
  static const int defaultLeaderboardSize = 10;
  static const int maxLeaderboardSize = 100;
  static const Duration leaderboardUpdateInterval = Duration(minutes: 5);
  
  // Real-time Update Intervals
  static const Duration dashboardUpdateInterval = Duration(seconds: 30);
  static const Duration notificationCheckInterval = Duration(seconds: 15);
  static const Duration userStatusUpdateInterval = Duration(minutes: 1);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  
  // Color Constants (Hex values)
  static const String primaryColorHex = '#2563EB';
  static const String secondaryColorHex = '#64748B';
  static const String successColorHex = '#059669';
  static const String warningColorHex = '#D97706';
  static const String errorColorHex = '#DC2626';
  static const String greyAppBarColorHex = '#F5F5F5';
  static const String backgroundColorHex = '#FFFFFF';
  static const String surfaceColorHex = '#F8FAFC';
  
  // Font Constants
  static const String primaryFontFamily = 'Inter';
  static const String secondaryFontFamily = 'Roboto';
  static const double baseFontSize = 14.0;
  static const double smallFontSize = 12.0;
  static const double largeFontSize = 16.0;
  static const double headingFontSize = 24.0;
  
  // Icon Constants
  static const double defaultIconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // Button Constants
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;
  static const double buttonBorderRadius = 8.0;
  
  // Input Field Constants
  static const double inputFieldHeight = 48.0;
  static const double inputFieldBorderRadius = 8.0;
  static const double inputFieldBorderWidth = 1.0;
  
  // Card Constants
  static const double cardBorderRadius = 12.0;
  static const double cardElevation = 2.0;
  static const double cardPadding = 16.0;
  
  // List Constants
  static const double listItemHeight = 72.0;
  static const double listItemPadding = 16.0;
  static const double listItemSpacing = 8.0;
  
  // Dialog Constants
  static const double dialogBorderRadius = 16.0;
  static const double dialogPadding = 24.0;
  static const double dialogMaxWidth = 400.0;
  
  // Snackbar Constants
  static const Duration snackbarDuration = Duration(seconds: 4);
  static const double snackbarBorderRadius = 8.0;
  
  // Progress Indicator Constants
  static const double progressIndicatorSize = 24.0;
  static const double largeProgressIndicatorSize = 48.0;
  static const double progressIndicatorStrokeWidth = 3.0;
  
  // Shimmer Constants
  static const Duration shimmerDuration = Duration(milliseconds: 1500);
  static const double shimmerBorderRadius = 8.0;
  
  // Search Constants
  static const int searchDebounceMs = 300;
  static const int minSearchLength = 2;
  static const int maxSearchResults = 50;
  
  // Refresh Constants
  static const double refreshTriggerDistance = 100.0;
  static const Duration refreshIndicatorDuration = Duration(seconds: 2);
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Theme mode enumeration
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Navigation type enumeration
enum NavigationType {
  bottom,
  side,
  rail,
}

/// Screen size helper class
class ScreenSize {
  static DeviceType getDeviceType(double width) {
    if (width < AppConstants.mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < AppConstants.tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < AppConstants.largeDesktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }
  
  static NavigationType getNavigationType(double width) {
    if (width < AppConstants.tabletBreakpoint) {
      return NavigationType.bottom;
    } else if (width < AppConstants.desktopBreakpoint) {
      return NavigationType.rail;
    } else {
      return NavigationType.side;
    }
  }
  
  static int getGridColumns(double width) {
    final deviceType = getDeviceType(width);
    switch (deviceType) {
      case DeviceType.mobile:
        return AppConstants.mobileGridColumns;
      case DeviceType.tablet:
        return AppConstants.tabletGridColumns;
      case DeviceType.desktop:
        return AppConstants.desktopGridColumns;
      case DeviceType.largeDesktop:
        return AppConstants.largeDesktopGridColumns;
    }
  }
}
