name: client
description: "Quester Flutter Client - Modern responsive quest management app"
publish_to: 'none'

version: 2.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Shared package integration
  shared:
    path: ../shared

  # State Management
  flutter_bloc: ^8.1.6        # Modern BLoC state management
  provider: ^6.1.2             # Provider pattern support
  equatable: ^2.0.5            # Value equality for state classes

  # Navigation & Routing
  go_router: ^14.6.2           # Declarative routing system

  # UI & Design
  cupertino_icons: ^1.0.8      # Cross-platform icons
  google_fonts: ^6.2.1         # Enhanced typography

  # Networking & Real-time
  http: ^1.1.0                 # HTTP client
  web_socket_channel: ^2.4.5   # WebSocket communication
  dio: ^5.4.0                  # Advanced HTTP client

  # Storage & Persistence
  shared_preferences: ^2.2.3   # Local storage
  hive: ^2.2.3                 # Fast key-value database
  hive_flutter: ^1.1.0         # Hive Flutter integration

  # JSON & Serialization
  json_annotation: ^4.9.0      # JSON annotations

  # Responsive Design & Layout
  responsive_framework: ^1.4.0 # Responsive design framework
  flutter_screenutil: ^5.9.3  # Screen size adaptation

  # Animations & UI Enhancements
  flutter_animate: ^4.5.0      # Modern animations
  shimmer: ^3.0.0              # Loading shimmer effects
  cached_network_image: ^3.3.1 # Cached image loading
  flutter_svg: ^2.0.10+1       # SVG support

  # Utilities & Helpers
  intl: ^0.19.0                # Internationalization
  uuid: ^4.4.0                 # UUID generation
  connectivity_plus: ^6.0.5    # Network connectivity
  device_info_plus: ^10.1.2    # Device information
  package_info_plus: ^8.0.2    # App package information

  # Authentication & Security
  crypto: ^3.0.6               # Cryptographic functions
  flutter_secure_storage: ^9.2.2 # Secure storage

  # Notifications & Permissions
  flutter_local_notifications: ^17.2.3 # Local notifications
  permission_handler: ^11.3.1  # Permission management
  timezone: ^0.9.4             # Timezone support for notifications

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code quality and linting
  flutter_lints: ^5.0.0        # Code quality and linting

  # Code generation
  build_runner: ^2.4.7         # Build system
  json_serializable: ^6.7.1    # JSON serialization generator

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  uses-material-design: true

  # Assets configuration
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
