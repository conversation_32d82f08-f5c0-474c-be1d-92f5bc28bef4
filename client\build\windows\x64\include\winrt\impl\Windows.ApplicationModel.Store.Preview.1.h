// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_ApplicationModel_Store_Preview_1_H
#define WINRT_Windows_ApplicationModel_Store_Preview_1_H
#include "winrt/impl/Windows.ApplicationModel.Store.Preview.0.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Store::Preview
{
    struct __declspec(empty_bases) IDeliveryOptimizationSettings :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeliveryOptimizationSettings>
    {
        IDeliveryOptimizationSettings(std::nullptr_t = nullptr) noexcept {}
        IDeliveryOptimizationSettings(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IDeliveryOptimizationSettingsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IDeliveryOptimizationSettingsStatics>
    {
        IDeliveryOptimizationSettingsStatics(std::nullptr_t = nullptr) noexcept {}
        IDeliveryOptimizationSettingsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConfigurationStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConfigurationStatics>
    {
        IStoreConfigurationStatics(std::nullptr_t = nullptr) noexcept {}
        IStoreConfigurationStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConfigurationStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConfigurationStatics2>
    {
        IStoreConfigurationStatics2(std::nullptr_t = nullptr) noexcept {}
        IStoreConfigurationStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConfigurationStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConfigurationStatics3>
    {
        IStoreConfigurationStatics3(std::nullptr_t = nullptr) noexcept {}
        IStoreConfigurationStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConfigurationStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConfigurationStatics4>
    {
        IStoreConfigurationStatics4(std::nullptr_t = nullptr) noexcept {}
        IStoreConfigurationStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreConfigurationStatics5 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreConfigurationStatics5>
    {
        IStoreConfigurationStatics5(std::nullptr_t = nullptr) noexcept {}
        IStoreConfigurationStatics5(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStoreHardwareManufacturerInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStoreHardwareManufacturerInfo>
    {
        IStoreHardwareManufacturerInfo(std::nullptr_t = nullptr) noexcept {}
        IStoreHardwareManufacturerInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePreview :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePreview>
    {
        IStorePreview(std::nullptr_t = nullptr) noexcept {}
        IStorePreview(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePreviewProductInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePreviewProductInfo>
    {
        IStorePreviewProductInfo(std::nullptr_t = nullptr) noexcept {}
        IStorePreviewProductInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePreviewPurchaseResults :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePreviewPurchaseResults>
    {
        IStorePreviewPurchaseResults(std::nullptr_t = nullptr) noexcept {}
        IStorePreviewPurchaseResults(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IStorePreviewSkuInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IStorePreviewSkuInfo>
    {
        IStorePreviewSkuInfo(std::nullptr_t = nullptr) noexcept {}
        IStorePreviewSkuInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IWebAuthenticationCoreManagerHelper :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWebAuthenticationCoreManagerHelper>
    {
        IWebAuthenticationCoreManagerHelper(std::nullptr_t = nullptr) noexcept {}
        IWebAuthenticationCoreManagerHelper(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
