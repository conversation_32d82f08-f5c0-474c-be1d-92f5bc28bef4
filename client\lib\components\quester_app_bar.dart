import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:go_router/go_router.dart';

import '../providers/notification_cubit.dart';
import '../components/notification_badge.dart';
import '../core/constants/app_constants.dart';

/// Elevated grey app bar with Quester logo and action icons
class QuesterAppBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onAccountTap;

  const QuesterAppBar({
    super.key,
    this.onAccountTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveBreakpoints.of(context).isDesktop;

    return AppBar(
      automaticallyImplyLeading: false,
      elevation: AppConstants.appBarElevation,
      backgroundColor: const Color(0xFFF5F5F5), // Grey app bar color
      foregroundColor: Colors.black87,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.black26,
      toolbarHeight: AppConstants.appBarHeight,

      title: Row(
        children: [
          // Quester logo and title
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.explore,
              size: isDesktop ? 28.sp : 24.sp,
              color: Colors.white,
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            AppConstants.appBarTitle,
            style: TextStyle(
              fontSize: isDesktop ? 24.sp : 20.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
      actions: [
        // Search icon
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              Icons.search,
              size: isDesktop ? 24.sp : 20.sp,
              color: Colors.black87,
            ),
            onPressed: () {
              _showSearchDialog(context);
            },
            tooltip: 'Search',
          ),
        ),

        // Notification icon with badge
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: BlocBuilder<NotificationCubit, NotificationState>(
            builder: (context, state) {
              final unreadCount = context.read<NotificationCubit>().unreadCount;

              return NotificationBadge(
                count: unreadCount,
                child: IconButton(
                  icon: Icon(
                    Icons.notifications_outlined,
                    size: isDesktop ? 24.sp : 20.sp,
                    color: Colors.black87,
                  ),
                  onPressed: () {
                    context.go('/notifications');
                  },
                  tooltip: 'Notifications',
                ),
              );
            },
          ),
        ),

        // Account icon
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              Icons.account_circle_outlined,
              size: isDesktop ? 24.sp : 20.sp,
              color: Colors.black87,
            ),
            onPressed: onAccountTap,
            tooltip: 'Account',
          ),
        ),

        SizedBox(width: 16.w),
      ],
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const _SearchDialog(),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(64);
}

class _SearchDialog extends StatefulWidget {
  const _SearchDialog();

  @override
  State<_SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<_SearchDialog> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus the search field when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search header
            Row(
              children: [
                Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Search',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Search field
            TextField(
              controller: _searchController,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: 'Search quests, users, or content...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _performSearch(value.trim());
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // Quick search suggestions
            if (_searchController.text.isEmpty) ...[
              Text(
                'Quick Search',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _SearchChip(
                    label: 'Active Quests',
                    icon: Icons.assignment,
                    onTap: () => _performSearch('active quests'),
                  ),
                  _SearchChip(
                    label: 'Completed',
                    icon: Icons.check_circle,
                    onTap: () => _performSearch('completed'),
                  ),
                  _SearchChip(
                    label: 'Notifications',
                    icon: Icons.notifications,
                    onTap: () => _performSearch('notifications'),
                  ),
                  _SearchChip(
                    label: 'Profile',
                    icon: Icons.person,
                    onTap: () => _performSearch('profile'),
                  ),
                ],
              ),
            ],
            
            // Search results (placeholder)
            if (_searchController.text.isNotEmpty) ...[
              Text(
                'Search Results',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Search functionality coming soon!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _performSearch(String query) {
    // TODO: Implement actual search functionality
    Navigator.of(context).pop();
    
    // For now, just show a snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Searching for: $query'),
      ),
    );
  }
}

class _SearchChip extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback onTap;

  const _SearchChip({
    required this.label,
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      onPressed: onTap,
    );
  }
}
