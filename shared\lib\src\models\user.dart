import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'user.g.dart';

/// User role enumeration
enum UserRole {
  @JsonValue('user')
  user,
  @JsonValue('admin')
  admin,
  @JsonValue('moderator')
  moderator,
}

/// Main user model with authentication and profile information
@JsonSerializable()
class User extends Equatable {
  final String id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final int xp;
  final int level;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final String? avatarUrl;
  final bool isActive;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.role = UserRole.user,
    this.xp = 0,
    this.level = 1,
    required this.createdAt,
    this.lastLoginAt,
    this.avatarUrl,
    this.isActive = true,
  });

  /// Create user from JSON
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Convert user to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// Create a copy of the user with updated fields
  User copyWith({
    String? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    UserRole? role,
    int? xp,
    int? level,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    String? avatarUrl,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      role: role ?? this.role,
      xp: xp ?? this.xp,
      level: level ?? this.level,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Get user's full name
  String get fullName => '$firstName $lastName';

  /// Check if user is admin
  bool get isAdmin => role == UserRole.admin;

  /// Check if user is moderator or admin
  bool get isModerator => role == UserRole.moderator || role == UserRole.admin;

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        firstName,
        lastName,
        role,
        xp,
        level,
        createdAt,
        lastLoginAt,
        avatarUrl,
        isActive,
      ];
}


