import 'dart:async';
import 'dart:convert';
import 'package:crypto/crypto.dart';

import 'api_service.dart';
import 'storage_service.dart';

/// Authentication service for handling user authentication
class AuthService {
  final ApiService _apiService;
  final StorageService _storageService;
  
  // Stream controllers for auth state
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  final StreamController<User?> _userController = StreamController<User?>.broadcast();
  
  AuthState _currentState = AuthState.unauthenticated;
  User? _currentUser;
  Timer? _tokenRefreshTimer;

  AuthService({
    required ApiService apiService,
    required StorageService storageService,
  })  : _apiService = apiService,
        _storageService = storageService {
    _initializeAuth();
  }

  // Getters
  AuthState get currentState => _currentState;
  User? get currentUser => _currentUser;
  Stream<AuthState> get authStateStream => _authStateController.stream;
  Stream<User?> get userStream => _userController.stream;
  bool get isAuthenticated => _currentState == AuthState.authenticated;

  /// Initialize authentication state
  Future<void> _initializeAuth() async {
    try {
      final token = await _storageService.getAuthToken();
      if (token != null) {
        // Validate token and get user data
        final userData = await _storageService.getUserData();
        if (userData != null) {
          _currentUser = User.fromJson(userData);
          _updateAuthState(AuthState.authenticated);
          _scheduleTokenRefresh();
        } else {
          // Token exists but no user data, try to fetch user
          await _fetchCurrentUser();
        }
      } else {
        _updateAuthState(AuthState.unauthenticated);
      }
    } catch (e) {
      print('Error initializing auth: $e');
      _updateAuthState(AuthState.unauthenticated);
    }
  }

  /// Update authentication state
  void _updateAuthState(AuthState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _authStateController.add(newState);
    }
  }

  /// Update current user
  void _updateUser(User? user) {
    _currentUser = user;
    _userController.add(user);
  }

  /// Register a new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String username,
    String? displayName,
  }) async {
    try {
      _updateAuthState(AuthState.loading);

      final hashedPassword = _hashPassword(password);
      
      final response = await _apiService.post('/auth/register', data: {
        'email': email,
        'password': hashedPassword,
        'username': username,
        'displayName': displayName,
      });

      if (response.success && response.data != null) {
        final authData = response.data as Map<String, dynamic>;
        await _handleAuthSuccess(authData);
        
        return AuthResult.success(
          message: 'Registration successful',
          user: _currentUser,
        );
      } else {
        _updateAuthState(AuthState.unauthenticated);
        return AuthResult.failure(
          message: response.message ?? 'Registration failed',
          errors: response.errors,
        );
      }
    } catch (e) {
      _updateAuthState(AuthState.unauthenticated);
      return AuthResult.failure(
        message: 'Registration failed: ${e.toString()}',
      );
    }
  }

  /// Login with email and password
  Future<AuthResult> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _updateAuthState(AuthState.loading);

      final hashedPassword = _hashPassword(password);
      
      final response = await _apiService.post('/auth/login', data: {
        'email': email,
        'password': hashedPassword,
        'rememberMe': rememberMe,
      });

      if (response.success && response.data != null) {
        final authData = response.data as Map<String, dynamic>;
        await _handleAuthSuccess(authData);
        
        return AuthResult.success(
          message: 'Login successful',
          user: _currentUser,
        );
      } else {
        _updateAuthState(AuthState.unauthenticated);
        return AuthResult.failure(
          message: response.message ?? 'Login failed',
          errors: response.errors,
        );
      }
    } catch (e) {
      _updateAuthState(AuthState.unauthenticated);
      return AuthResult.failure(
        message: 'Login failed: ${e.toString()}',
      );
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      // Call logout endpoint
      await _apiService.post('/auth/logout');
    } catch (e) {
      print('Error calling logout endpoint: $e');
    } finally {
      // Clear local data regardless of API call result
      await _clearAuthData();
      _updateAuthState(AuthState.unauthenticated);
      _updateUser(null);
      _cancelTokenRefresh();
    }
  }

  /// Refresh authentication token
  Future<bool> refreshToken() async {
    try {
      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null) {
        await logout();
        return false;
      }

      final response = await _apiService.post('/auth/refresh', data: {
        'refreshToken': refreshToken,
      });

      if (response.success && response.data != null) {
        final authData = response.data as Map<String, dynamic>;
        await _handleAuthSuccess(authData);
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (e) {
      print('Error refreshing token: $e');
      await logout();
      return false;
    }
  }

  /// Fetch current user data
  Future<void> _fetchCurrentUser() async {
    try {
      final response = await _apiService.get('/users/me');
      
      if (response.success && response.data != null) {
        final userData = response.data as Map<String, dynamic>;
        final user = User.fromJson(userData);
        
        await _storageService.saveUserData(userData);
        _updateUser(user);
        _updateAuthState(AuthState.authenticated);
        _scheduleTokenRefresh();
      } else {
        await logout();
      }
    } catch (e) {
      print('Error fetching current user: $e');
      await logout();
    }
  }

  /// Handle successful authentication
  Future<void> _handleAuthSuccess(Map<String, dynamic> authData) async {
    final token = authData['token'] as String?;
    final refreshToken = authData['refreshToken'] as String?;
    final userData = authData['user'] as Map<String, dynamic>?;

    if (token != null && userData != null) {
      // Save tokens
      await _storageService.saveAuthToken(token);
      if (refreshToken != null) {
        await _storageService.saveRefreshToken(refreshToken);
      }

      // Save user data
      await _storageService.saveUserData(userData);
      
      // Update state
      final user = User.fromJson(userData);
      _updateUser(user);
      _updateAuthState(AuthState.authenticated);
      
      // Schedule token refresh
      _scheduleTokenRefresh();
    } else {
      throw Exception('Invalid auth response data');
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    await _storageService.clearUserData();
  }

  /// Hash password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Schedule automatic token refresh
  void _scheduleTokenRefresh() {
    _cancelTokenRefresh();
    
    // Refresh token every 50 minutes (assuming 1-hour token expiry)
    _tokenRefreshTimer = Timer.periodic(
      const Duration(minutes: 50),
      (_) => refreshToken(),
    );
  }

  /// Cancel token refresh timer
  void _cancelTokenRefresh() {
    _tokenRefreshTimer?.cancel();
    _tokenRefreshTimer = null;
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
    _userController.close();
    _cancelTokenRefresh();
  }
}

/// Authentication state enumeration
enum AuthState {
  loading,
  authenticated,
  unauthenticated,
}

/// Authentication result class
class AuthResult {
  final bool success;
  final String message;
  final User? user;
  final Map<String, dynamic>? errors;

  AuthResult._({
    required this.success,
    required this.message,
    this.user,
    this.errors,
  });

  factory AuthResult.success({
    required String message,
    User? user,
  }) {
    return AuthResult._(
      success: true,
      message: message,
      user: user,
    );
  }

  factory AuthResult.failure({
    required String message,
    Map<String, dynamic>? errors,
  }) {
    return AuthResult._(
      success: false,
      message: message,
      errors: errors,
    );
  }
}

/// User model (simplified version)
class User {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.avatarUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      username: json['username'] as String,
      displayName: json['displayName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
