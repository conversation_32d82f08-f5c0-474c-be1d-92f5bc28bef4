import 'dart:async';
import '../models/models.dart';

/// Abstract WebSocket service interface for real-time communication
abstract class WebSocketService {
  /// Connection management
  void connect(String url);
  void disconnect();
  bool get isConnected;
  
  /// Connection state stream
  Stream<WebSocketConnectionState> get connectionStateStream;
  
  /// Message handling
  void sendMessage(Map<String, dynamic> message);
  Stream<Map<String, dynamic>> get messageStream;
  
  /// Notification handling
  void sendNotification(Notification notification);
  Stream<Notification> get notificationStream;
  
  /// Event subscriptions
  void subscribe(String event, Function(Map<String, dynamic>) callback);
  void unsubscribe(String event);
  
  /// Error handling
  Stream<WebSocketError> get errorStream;
  
  /// Reconnection settings
  void setReconnectionSettings({
    bool autoReconnect = true,
    Duration initialDelay = const Duration(seconds: 1),
    Duration maxDelay = const Duration(seconds: 30),
    int maxRetries = 5,
  });
}

// WebSocketConnectionState and WebSocketError are now imported from models.dart
  final DateTime timestamp;
  final dynamic originalError;

  const WebSocketError({
    required this.message,
    this.code,
    required this.timestamp,
    this.originalError,
  });

  @override
  String toString() => 'WebSocketError: $message';
}

/// WebSocket message types for the Quester application
class WebSocketMessageTypes {
  static const String ping = 'ping';
  static const String pong = 'pong';
  static const String notification = 'notification';
  static const String questUpdate = 'quest_update';
  static const String userUpdate = 'user_update';
  static const String dashboardUpdate = 'dashboard_update';
  static const String systemMessage = 'system_message';
  static const String error = 'error';
  static const String subscribe = 'subscribe';
  static const String unsubscribe = 'unsubscribe';
}

/// WebSocket message wrapper
class WebSocketMessage {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? id;

  const WebSocketMessage({
    required this.type,
    required this.data,
    required this.timestamp,
    this.id,
  });

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: json['type'] as String,
      data: json['data'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      id: json['id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      if (id != null) 'id': id,
    };
  }

  /// Create a ping message
  factory WebSocketMessage.ping() {
    return WebSocketMessage(
      type: WebSocketMessageTypes.ping,
      data: {},
      timestamp: DateTime.now(),
    );
  }

  /// Create a pong message
  factory WebSocketMessage.pong() {
    return WebSocketMessage(
      type: WebSocketMessageTypes.pong,
      data: {},
      timestamp: DateTime.now(),
    );
  }

  /// Create a notification message
  factory WebSocketMessage.notification(Notification notification) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.notification,
      data: notification.toJson(),
      timestamp: DateTime.now(),
      id: notification.id,
    );
  }

  /// Create a quest update message
  factory WebSocketMessage.questUpdate(Quest quest) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.questUpdate,
      data: quest.toJson(),
      timestamp: DateTime.now(),
      id: quest.id,
    );
  }

  /// Create a user update message
  factory WebSocketMessage.userUpdate(User user) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.userUpdate,
      data: user.toJson(),
      timestamp: DateTime.now(),
      id: user.id,
    );
  }

  /// Create a dashboard update message
  factory WebSocketMessage.dashboardUpdate(DashboardStats stats) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.dashboardUpdate,
      data: stats.toJson(),
      timestamp: DateTime.now(),
    );
  }

  /// Create a system message
  factory WebSocketMessage.systemMessage(String message, {Map<String, dynamic>? data}) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.systemMessage,
      data: {
        'message': message,
        ...?data,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create an error message
  factory WebSocketMessage.error(String message, {String? code}) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.error,
      data: {
        'message': message,
        if (code != null) 'code': code,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create a subscription message
  factory WebSocketMessage.subscribe(String event, {Map<String, dynamic>? params}) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.subscribe,
      data: {
        'event': event,
        if (params != null) 'params': params,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create an unsubscription message
  factory WebSocketMessage.unsubscribe(String event) {
    return WebSocketMessage(
      type: WebSocketMessageTypes.unsubscribe,
      data: {
        'event': event,
      },
      timestamp: DateTime.now(),
    );
  }
}
