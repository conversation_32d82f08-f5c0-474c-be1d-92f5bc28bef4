import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../services/auth_service.dart';

/// Authentication states
abstract class AuthState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final User user;
  final String token;

  AuthAuthenticated({required this.user, required this.token});

  @override
  List<Object?> get props => [user, token];
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  AuthError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Simple authentication management cubit
class AuthCubit extends Cubit<AuthState> {
  final AuthService? _authService;

  AuthCubit({AuthService? authService}) 
      : _authService = authService,
        super(AuthInitial()) {
    _checkAuthStatus();
  }

  /// Check if user is already authenticated
  Future<void> _checkAuthStatus() async {
    try {
      if (_authService != null && _authService!.isAuthenticated) {
        final user = _authService!.currentUser;
        if (user != null) {
          emit(AuthAuthenticated(user: user, token: 'token'));
        } else {
          emit(AuthUnauthenticated());
        }
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: 'Failed to check authentication status'));
    }
  }

  /// Login with email and password
  Future<void> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    emit(AuthLoading());
    
    try {
      if (_authService != null) {
        final result = await _authService!.login(
          email: email,
          password: password,
          rememberMe: rememberMe,
        );
        
        if (result.success && result.user != null) {
          emit(AuthAuthenticated(user: result.user!, token: 'token'));
        } else {
          emit(AuthError(message: result.message));
        }
      } else {
        emit(AuthError(message: 'Authentication service not available'));
      }
    } catch (e) {
      emit(AuthError(message: 'Login failed: ${e.toString()}'));
    }
  }

  /// Register new user
  Future<void> register({
    required String email,
    required String password,
    required String username,
    String? displayName,
  }) async {
    emit(AuthLoading());
    
    try {
      if (_authService != null) {
        final result = await _authService!.register(
          email: email,
          password: password,
          username: username,
          displayName: displayName,
        );
        
        if (result.success && result.user != null) {
          emit(AuthAuthenticated(user: result.user!, token: 'token'));
        } else {
          emit(AuthError(message: result.message));
        }
      } else {
        emit(AuthError(message: 'Authentication service not available'));
      }
    } catch (e) {
      emit(AuthError(message: 'Registration failed: ${e.toString()}'));
    }
  }

  /// Logout user
  Future<void> logout() async {
    emit(AuthLoading());
    
    try {
      if (_authService != null) {
        await _authService!.logout();
      }
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, clear local state
      emit(AuthUnauthenticated());
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    try {
      if (_authService != null) {
        final success = await _authService!.refreshToken();
        if (!success) {
          emit(AuthUnauthenticated());
        }
      }
    } catch (e) {
      emit(AuthUnauthenticated());
    }
  }

  /// Get current user
  User? get currentUser {
    final currentState = state;
    if (currentState is AuthAuthenticated) {
      return currentState.user;
    }
    return null;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  /// Check if authentication is loading
  bool get isLoading => state is AuthLoading;

  /// Get current error message
  String? get errorMessage {
    final currentState = state;
    if (currentState is AuthError) {
      return currentState.message;
    }
    return null;
  }

  /// Clear error state
  void clearError() {
    if (state is AuthError) {
      emit(AuthUnauthenticated());
    }
  }
}
