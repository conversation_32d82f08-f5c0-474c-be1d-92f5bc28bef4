import 'dart:async';

import 'websocket_service.dart';
import 'storage_service.dart';

/// Service for handling real-time updates and synchronization
class RealtimeService {
  final WebSocketService _webSocketService;
  final StorageService _storageService;
  
  // Stream controllers for different types of real-time updates
  final StreamController<Map<String, dynamic>> _dashboardUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _questUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _userUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _achievementUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _leaderboardUpdatesController =
      StreamController<Map<String, dynamic>>.broadcast();
  
  StreamSubscription? _messageSubscription;
  Timer? _heartbeatTimer;
  bool _isInitialized = false;

  RealtimeService({
    required WebSocketService webSocketService,
    required StorageService storageService,
  })  : _webSocketService = webSocketService,
        _storageService = storageService;

  // Public streams
  Stream<Map<String, dynamic>> get dashboardUpdates => _dashboardUpdatesController.stream;
  Stream<Map<String, dynamic>> get questUpdates => _questUpdatesController.stream;
  Stream<Map<String, dynamic>> get userUpdates => _userUpdatesController.stream;
  Stream<Map<String, dynamic>> get achievementUpdates => _achievementUpdatesController.stream;
  Stream<Map<String, dynamic>> get leaderboardUpdates => _leaderboardUpdatesController.stream;

  /// Initialize the real-time service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Listen to WebSocket messages
    _messageSubscription = _webSocketService.messageStream.listen(_handleMessage);
    
    // Start heartbeat timer
    _startHeartbeat();
    
    _isInitialized = true;
    print('🔄 Real-time service initialized');
  }

  /// Dispose the service
  void dispose() {
    _messageSubscription?.cancel();
    _heartbeatTimer?.cancel();
    
    _dashboardUpdatesController.close();
    _questUpdatesController.close();
    _userUpdatesController.close();
    _achievementUpdatesController.close();
    _leaderboardUpdatesController.close();
    
    _isInitialized = false;
    print('🔄 Real-time service disposed');
  }

  /// Subscribe to real-time updates for a specific channel
  void subscribeToChannel(String channel) {
    if (!_webSocketService.isConnected) {
      print('⚠️ Cannot subscribe to $channel: WebSocket not connected');
      return;
    }

    _webSocketService.subscribe(channel);
    print('📡 Subscribed to channel: $channel');
  }

  /// Unsubscribe from real-time updates for a specific channel
  void unsubscribeFromChannel(String channel) {
    if (!_webSocketService.isConnected) {
      print('⚠️ Cannot unsubscribe from $channel: WebSocket not connected');
      return;
    }

    _webSocketService.unsubscribe(channel);
    print('📡 Unsubscribed from channel: $channel');
  }

  /// Subscribe to dashboard updates
  void subscribeToDashboard() {
    subscribeToChannel('dashboard');
  }

  /// Subscribe to quest updates
  void subscribeToQuests() {
    subscribeToChannel('quests');
  }

  /// Subscribe to user updates
  void subscribeToUser() {
    subscribeToChannel('user');
  }

  /// Subscribe to achievement updates
  void subscribeToAchievements() {
    subscribeToChannel('achievements');
  }

  /// Subscribe to leaderboard updates
  void subscribeToLeaderboard() {
    subscribeToChannel('leaderboard');
  }

  /// Subscribe to all relevant channels for the current user
  void subscribeToAllChannels() {
    subscribeToDashboard();
    subscribeToQuests();
    subscribeToUser();
    subscribeToAchievements();
    subscribeToLeaderboard();
  }

  /// Unsubscribe from all channels
  void unsubscribeFromAllChannels() {
    unsubscribeFromChannel('dashboard');
    unsubscribeFromChannel('quests');
    unsubscribeFromChannel('user');
    unsubscribeFromChannel('achievements');
    unsubscribeFromChannel('leaderboard');
  }

  /// Handle incoming WebSocket messages
  void _handleMessage(Map<String, dynamic> message) {
    try {
      final messageType = message['type'] as String?;
      final channel = message['channel'] as String?;
      final data = message['data'] as Map<String, dynamic>?;

      if (messageType == null || data == null) {
        print('⚠️ Invalid message format received');
        return;
      }

      switch (messageType) {
        case 'dashboard_update':
          _handleDashboardUpdate(data);
          break;
        case 'quest_update':
          _handleQuestUpdate(data);
          break;
        case 'user_update':
          _handleUserUpdate(data);
          break;
        case 'achievement_update':
          _handleAchievementUpdate(data);
          break;
        case 'leaderboard_update':
          _handleLeaderboardUpdate(data);
          break;
        case 'notification':
          // Notifications are handled by the WebSocket cubit
          break;
        case 'pong':
          _handlePong(data);
          break;
        default:
          print('🔄 Unknown message type: $messageType');
      }
    } catch (e) {
      print('❌ Error handling real-time message: $e');
    }
  }

  /// Handle dashboard updates
  void _handleDashboardUpdate(Map<String, dynamic> data) {
    print('📊 Dashboard update received');
    _dashboardUpdatesController.add(data);
    
    // Cache the dashboard data
    _storageService.setCachedData(
      'dashboard_stats',
      data,
      expiration: const Duration(minutes: 5),
    );
  }

  /// Handle quest updates
  void _handleQuestUpdate(Map<String, dynamic> data) {
    print('🎯 Quest update received');
    _questUpdatesController.add(data);
    
    // Update cached quest data if needed
    final questId = data['id'] as String?;
    if (questId != null) {
      _storageService.setCachedData(
        'quest_$questId',
        data,
        expiration: const Duration(minutes: 10),
      );
    }
  }

  /// Handle user updates
  void _handleUserUpdate(Map<String, dynamic> data) {
    print('👤 User update received');
    _userUpdatesController.add(data);
    
    // Update cached user data
    _storageService.setCachedData(
      'user_profile',
      data,
      expiration: const Duration(minutes: 15),
    );
  }

  /// Handle achievement updates
  void _handleAchievementUpdate(Map<String, dynamic> data) {
    print('🏆 Achievement update received');
    _achievementUpdatesController.add(data);
    
    // Cache achievement data
    final achievementId = data['id'] as String?;
    if (achievementId != null) {
      _storageService.setCachedData(
        'achievement_$achievementId',
        data,
        expiration: const Duration(hours: 1),
      );
    }
  }

  /// Handle leaderboard updates
  void _handleLeaderboardUpdate(Map<String, dynamic> data) {
    print('🏅 Leaderboard update received');
    _leaderboardUpdatesController.add(data);
    
    // Cache leaderboard data
    final leaderboardId = data['id'] as String?;
    if (leaderboardId != null) {
      _storageService.setCachedData(
        'leaderboard_$leaderboardId',
        data,
        expiration: const Duration(minutes: 5),
      );
    }
  }

  /// Handle pong response
  void _handlePong(Map<String, dynamic> data) {
    final timestamp = data['timestamp'] as String?;
    if (timestamp != null) {
      final sentTime = DateTime.tryParse(timestamp);
      if (sentTime != null) {
        final latency = DateTime.now().difference(sentTime);
        print('🏓 Pong received - Latency: ${latency.inMilliseconds}ms');
      }
    }
  }

  /// Start heartbeat timer
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _sendHeartbeat();
    });
  }

  /// Send heartbeat message
  void _sendHeartbeat() {
    if (_webSocketService.isConnected) {
      _webSocketService.sendMessage({
        'type': 'ping',
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Send a custom real-time message
  void sendMessage(String type, Map<String, dynamic> data, {String? channel}) {
    final message = <String, dynamic>{
      'type': type,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (channel != null) {
      message['channel'] = channel;
    }

    _webSocketService.sendMessage(message);
  }

  /// Request real-time data refresh
  void requestDataRefresh(String dataType) {
    sendMessage('request_refresh', {'dataType': dataType});
  }

  /// Request dashboard refresh
  void requestDashboardRefresh() {
    requestDataRefresh('dashboard');
  }

  /// Request quest refresh
  void requestQuestRefresh() {
    requestDataRefresh('quests');
  }

  /// Request user profile refresh
  void requestUserRefresh() {
    requestDataRefresh('user');
  }

  /// Get connection status
  bool get isConnected => _webSocketService.isConnected;

  /// Get initialization status
  bool get isInitialized => _isInitialized;
}
