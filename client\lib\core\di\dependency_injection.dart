import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// Services
import '../../services/api_service.dart';
import '../../services/websocket_service.dart';
import '../../services/storage_service.dart';
import '../../services/auth_service.dart';
import '../../services/notification_service.dart';

// Providers/Cubits
import '../../providers/theme_cubit.dart';
import '../../providers/auth_cubit.dart';
import '../../providers/notification_cubit.dart';
import '../../providers/websocket_cubit.dart';

// Repositories
import '../../repositories/auth_repository.dart';
import '../../repositories/user_repository.dart';
import '../../repositories/quest_repository.dart';
import '../../repositories/notification_repository.dart';

/// Simple service locator for dependency injection
class DependencyInjection {
  static final Map<Type, dynamic> _services = {};
  static bool _initialized = false;

  /// Initialize all dependencies
  static Future<void> init() async {
    if (_initialized) return;

    // Core services
    await _registerCoreServices();
    
    // Repositories
    await _registerRepositories();
    
    // Cubits/Providers
    await _registerCubits();
    
    _initialized = true;
  }

  /// Register core services
  static Future<void> _registerCoreServices() async {
    // Shared Preferences
    final sharedPreferences = await SharedPreferences.getInstance();
    _services[SharedPreferences] = sharedPreferences;

    // Secure Storage
    const secureStorage = FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );
    _services[FlutterSecureStorage] = secureStorage;

    // Dio HTTP client
    final dio = Dio();
    dio.options.baseUrl = 'http://localhost:8080/api';
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);
    
    // Add interceptors
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) {
        // In production, you might want to use a proper logging library
        // For now, we'll use debugPrint which is safe for production
        debugPrint(object.toString());
      },
    ));
    
    _services[Dio] = dio;

    // Storage Service
    final storageService = StorageService(
      sharedPreferences: sharedPreferences,
      secureStorage: secureStorage,
    );
    _services[StorageService] = storageService;

    // API Service
    final apiService = ApiService();
    _services[ApiService] = apiService;

    // WebSocket Service
    final webSocketService = WebSocketService();
    _services[WebSocketService] = webSocketService;

    // Auth Service
    final authService = AuthService(
      apiService: apiService,
      storageService: storageService,
    );
    _services[AuthService] = authService;

    // Notification Service
    final notificationService = NotificationService();
    await notificationService.initialize();
    _services[NotificationService] = notificationService;
  }

  /// Register repositories
  static Future<void> _registerRepositories() async {
    final apiService = get<ApiService>();
    final storageService = get<StorageService>();

    // Auth Repository
    final authRepository = AuthRepository(
      apiService: apiService,
      storageService: storageService,
    );
    _services[AuthRepository] = authRepository;

    // User Repository
    final userRepository = UserRepository(apiService: apiService);
    _services[UserRepository] = userRepository;

    // Quest Repository
    final questRepository = QuestRepository(apiService: apiService);
    _services[QuestRepository] = questRepository;

    // Notification Repository
    final notificationRepository = NotificationRepository(apiService: apiService);
    _services[NotificationRepository] = notificationRepository;
  }

  /// Register cubits/providers
  static Future<void> _registerCubits() async {
    final storageService = get<StorageService>();
    final webSocketService = get<WebSocketService>();
    final apiService = get<ApiService>();

    // Theme Cubit
    final themeCubit = ThemeCubit(storageService: storageService);
    _services[ThemeCubit] = themeCubit;

    // Auth Cubit
    final authCubit = AuthCubit(
      apiService: apiService,
      webSocketService: webSocketService,
    );
    _services[AuthCubit] = authCubit;

    // WebSocket Cubit
    final webSocketCubit = WebSocketCubit(webSocketService: webSocketService);
    _services[WebSocketCubit] = webSocketCubit;

    // Notification Cubit
    final notificationCubit = NotificationCubit();
    _services[NotificationCubit] = notificationCubit;
  }

  /// Get a service by type
  static T get<T>() {
    final service = _services[T];
    if (service == null) {
      throw Exception('Service of type $T is not registered');
    }
    return service as T;
  }

  /// Check if a service is registered
  static bool isRegistered<T>() {
    return _services.containsKey(T);
  }

  /// Register a service manually
  static void register<T>(T service) {
    _services[T] = service;
  }

  /// Unregister a service
  static void unregister<T>() {
    _services.remove(T);
  }

  /// Clear all services (for testing)
  static void clear() {
    _services.clear();
    _initialized = false;
  }

  /// Reset and reinitialize
  static Future<void> reset() async {
    clear();
    await init();
  }

  /// Get all registered service types
  static List<Type> get registeredTypes => _services.keys.toList();

  /// Check if DI is initialized
  static bool get isInitialized => _initialized;
}
